[project]
name = "fantasy-ai"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.10.11"
dependencies = [
    "beautifulsoup4>=4.13.5",
    "espn-api>=0.45.1",
    "lxml>=6.0.1",
    "pandas>=2.3.2",
    "python-dotenv>=1.1.1",
    "schedule>=1.2.2",
    "pytest>=8.0.0",
    "jinja2",
    "duckdb>=1.0.0",
    "requests>=2.31.0",
]

[tool.setuptools.packages.find]
where = ["src"]