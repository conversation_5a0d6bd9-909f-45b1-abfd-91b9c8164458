"""
Start/Sit Analysis CLI Command

Command class for the CLI system to generate start/sit accuracy analysis.
"""

import os
import sys
from pathlib import Path
from typing import Optional
from datetime import datetime
import logging
import webbrowser

from ..start_sit_accuracy_analyzer import StartSitAccuracyAnalyzer
from ..start_sit_report_generator import StartSitReportGenerator
from ..projection_storage import ProjectionStorage
from ..html_generator import HtmlGenerator


class StartSitAnalysisCommand:
    """CLI command for start/sit accuracy analysis."""
    
    def add_arguments(self, parser):
        """Add arguments to the CLI parser."""
        parser.add_argument(
            "--team", "-t", 
            type=str, 
            help="Specific team name to analyze (if not provided, analyzes entire league)"
        )
        parser.add_argument(
            "--output", "-o", 
            type=str, 
            help="Output HTML file path"
        )
        parser.add_argument(
            "--no-projections", 
            action="store_true",
            help="Skip external projection source comparisons"
        )
        parser.add_argument(
            "--show-summary",
            action="store_true",
            help="Show summary statistics in console"
        )
    
    def handle(self, args, fm=None):
        """Handle the start/sit analysis command."""
        # Setup logging
        if args.verbose:
            logging.basicConfig(level=logging.DEBUG)
        else:
            logging.basicConfig(level=logging.INFO)
        logger = logging.getLogger(__name__)
        
        try:
            # Use the fantasy manager's league if provided
            if fm:
                league = fm.league
                logger.info("Using existing league connection")
            else:
                raise ValueError("Fantasy Manager required for start/sit analysis")
            
            # Determine week to analyze
            week = args.week
            if week is None:
                # Use most recent completed week
                week = league.current_week - 1
                if week < 1:
                    week = 1
                logger.info(f"No week specified, analyzing Week {week}")
            
            print(f"📊 Analyzing start/sit decisions for Week {week}")
            
            # Initialize analyzers
            start_sit_analyzer = StartSitAccuracyAnalyzer(league)
            html_generator = HtmlGenerator()
            report_generator = StartSitReportGenerator(html_generator)
            
            # Load external projections if requested
            external_projections = None
            if not args.no_projections:
                try:
                    projection_storage = ProjectionStorage()
                    external_projections = projection_storage.load_week_projections(week)
                    if external_projections:
                        print(f"📈 Loaded projections from {len(external_projections)} sources")
                except Exception as e:
                    logger.warning(f"Could not load external projections: {e}")
                    print("⚠️  External projections not available, using ESPN data only")
            
            # Generate analysis
            if args.team:
                # Single team analysis
                print(f"🔍 Analyzing team: {args.team}")
                
                # Find the specified team
                target_team = None
                for team in league.teams:
                    if team.team_name.lower() == args.team.lower():
                        target_team = team
                        break
                
                if not target_team:
                    available_teams = [team.team_name for team in league.teams]
                    print(f"❌ Team '{args.team}' not found in league.")
                    print(f"Available teams: {', '.join(available_teams)}")
                    return
                
                # Analyze team performance
                team_performance = start_sit_analyzer.analyze_team_start_sit_performance(
                    team=target_team,
                    week=week,
                    projection_sources=external_projections
                )
                
                # Get league context for comparison
                league_intelligence = start_sit_analyzer.analyze_league_start_sit_intelligence(
                    week=week,
                    projection_sources=external_projections
                )
                
                # Generate team-specific report
                html_content = report_generator.generate_team_start_sit_report(
                    team_performance, league_intelligence
                )
                
                # Show team summary if requested
                if args.show_summary or args.verbose:
                    self._print_team_summary(team_performance, league_intelligence)
                
            else:
                # League-wide analysis
                print("🏆 Analyzing entire league")
                
                # Generate league intelligence
                league_intelligence = start_sit_analyzer.analyze_league_start_sit_intelligence(
                    week=week,
                    projection_sources=external_projections
                )
                
                # Generate projection comparisons if data available
                projection_comparisons = None
                if external_projections:
                    try:
                        projection_comparisons = start_sit_analyzer.compare_projection_sources_for_start_sit(
                            week=week,
                            projection_sources=external_projections
                        )
                        print(f"📊 Generated projection source comparisons")
                    except Exception as e:
                        logger.warning(f"Could not generate projection comparisons: {e}")
                
                # Generate league report
                html_content = report_generator.generate_league_start_sit_report(
                    league_intelligence, projection_comparisons
                )
                
                # Show league summary
                if args.show_summary or args.verbose or not args.team:
                    self._print_league_summary(league_intelligence)
            
            # Determine output file
            output_file = args.output
            if output_file is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                if args.team:
                    safe_team_name = args.team.replace(" ", "_").replace("/", "_")
                    output_file = f"start_sit_analysis_{safe_team_name}_week_{week}_{timestamp}.html"
                else:
                    output_file = f"start_sit_analysis_league_week_{week}_{timestamp}.html"
            
            # Write HTML report
            output_path = Path(output_file)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            print(f"✅ Start/sit analysis report generated: {output_path}")
            
            # Open in browser if requested
            if args.open:
                try:
                    webbrowser.open(f"file://{output_path.absolute()}")
                    print("🌐 Opened report in browser")
                except Exception as e:
                    logger.warning(f"Could not open browser: {e}")
            
        except Exception as e:
            print(f"❌ Start/sit analysis failed: {e}")
            if args.verbose:
                import traceback
                traceback.print_exc()
            raise
    
    def _print_team_summary(self, team_performance, league_intelligence):
        """Print team-specific summary to console."""
        print(f"\n📋 {team_performance.team_name} - Week {team_performance.week} Summary")
        print("=" * 60)
        print(f"🎯 Lineup Efficiency: {team_performance.lineup_efficiency:.1f}%")
        print(f"📈 Starting Points: {team_performance.total_starting_points:.1f}")
        print(f"⚡ Optimal Possible: {team_performance.optimal_lineup_points:.1f}")
        print(f"🔍 Points Left on Bench: {team_performance.points_left_on_bench:.1f}")
        
        # League comparison
        efficiency_rank = self._calculate_team_ranking(
            team_performance, league_intelligence.team_performances, 'lineup_efficiency'
        )
        bench_rank = self._calculate_team_ranking(
            team_performance, league_intelligence.team_performances, 'points_left_on_bench', reverse=True
        )
        
        print(f"\n🏆 League Rankings:")
        print(f"   Efficiency: #{efficiency_rank} of {len(league_intelligence.team_performances)}")
        print(f"   Points on Bench: #{bench_rank} of {len(league_intelligence.team_performances)} (lower is better)")
        
        # Best/worst decisions
        if team_performance.best_start_decision:
            best = team_performance.best_start_decision
            print(f"\n✅ Best Decision: {best.player_name} ({best.position}) - "
                  f"+{best.decision_impact:.1f} vs best bench alternative")
        
        if team_performance.worst_start_decision:
            worst = team_performance.worst_start_decision
            print(f"❌ Worst Decision: {worst.player_name} ({worst.position}) - "
                  f"{worst.decision_impact:.1f} vs best bench alternative")
        
        # Missed opportunities
        if team_performance.missed_opportunities:
            print(f"\n🚫 Major Missed Opportunities ({len(team_performance.missed_opportunities)}):")
            for miss in team_performance.missed_opportunities[:3]:  # Top 3
                print(f"   • Should have started {miss.player_name} ({miss.position}) - "
                      f"missed {abs(miss.decision_impact):.1f} points")
    
    def _print_league_summary(self, league_intelligence):
        """Print league-wide summary to console."""
        print(f"\n🏆 Week {league_intelligence.week} League Start/Sit Summary")
        print("=" * 60)
        print(f"📊 Average Lineup Efficiency: {league_intelligence.league_avg_lineup_efficiency:.1f}%")
        print(f"🔍 Average Points on Bench: {league_intelligence.league_avg_points_on_bench:.1f}")
        print(f"📋 Teams Analyzed: {len(league_intelligence.team_performances)}")
        print(f"🥇 Best Decision Maker: {league_intelligence.best_decision_maker}")
        print(f"🥉 Most Points Left on Bench: {league_intelligence.most_points_left_on_bench}")
        
        # Show top 3 teams by efficiency
        sorted_teams = sorted(
            league_intelligence.team_performances,
            key=lambda x: x.lineup_efficiency,
            reverse=True
        )
        
        print(f"\n🏆 Top 3 Teams by Lineup Efficiency:")
        for i, team_perf in enumerate(sorted_teams[:3], 1):
            print(f"  {i}. {team_perf.team_name}: {team_perf.lineup_efficiency:.1f}% "
                  f"({team_perf.points_left_on_bench:.1f} pts on bench)")
        
        # Show bottom 3 teams
        print(f"\n📉 Bottom 3 Teams by Lineup Efficiency:")
        for i, team_perf in enumerate(sorted_teams[-3:], len(sorted_teams) - 2):
            print(f"  {i}. {team_perf.team_name}: {team_perf.lineup_efficiency:.1f}% "
                  f"({team_perf.points_left_on_bench:.1f} pts on bench)")
        
        # League-wide best/worst decisions
        if league_intelligence.best_start_decision_league_wide:
            best = league_intelligence.best_start_decision_league_wide
            print(f"\n🌟 Best Start Decision (League): {best.team_name} started "
                  f"{best.player_name} (+{best.decision_impact:.1f} pts)")
        
        if league_intelligence.worst_start_decision_league_wide:
            worst = league_intelligence.worst_start_decision_league_wide
            print(f"💔 Worst Start Decision (League): {worst.team_name} started "
                  f"{worst.player_name} ({worst.decision_impact:.1f} pts)")
        
        # Position trends
        if league_intelligence.position_trends:
            print(f"\n📊 Most Difficult Positions (Highest Points Left on Bench):")
            sorted_positions = sorted(
                league_intelligence.position_trends.items(),
                key=lambda x: x[1]['avg_points_left_on_bench'],
                reverse=True
            )
            for pos, trends in sorted_positions[:3]:
                print(f"   {pos}: {trends['avg_points_left_on_bench']:.1f} pts avg left on bench")
    
    def _calculate_team_ranking(self, target_team, all_teams, metric, reverse=False):
        """Calculate team's ranking for a specific metric."""
        sorted_teams = sorted(all_teams, key=lambda x: getattr(x, metric), reverse=not reverse)
        for i, team in enumerate(sorted_teams, 1):
            if team.team_name == target_team.team_name:
                return i
        return len(all_teams)  # Default to last place if not found