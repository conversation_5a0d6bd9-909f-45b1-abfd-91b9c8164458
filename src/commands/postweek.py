"""
Handles the postweek command following established patterns.
"""

import json
import os
from datetime import datetime
from jinja2 import Environment, FileSystemLoader

from .base import BaseCommand
from src.post_week_analyzer import PostWeekAnalyzer
from src.projection_storage import ProjectionStorage
from src.fanduel_projections import get_fanduel_projections
from src.winwithodds_projections import WinWithOddsProjections
from src.html_generator import HtmlGenerator
from src.strategic_decision_analyzer import StrategicDecisionAnalyzer

class PostweekCommand(BaseCommand):
    """A command to analyze projection accuracy after week completion."""

    def add_arguments(self, parser):
        """Add command-specific arguments to the parser."""
        # --week is now provided by the parent parser
        parser.add_argument("--html", action="store_true", help="Generate HTML report")
        parser.add_argument("--save", type=str, help="Save analysis to file (CSV or JSON)")
        parser.add_argument("--email", action="store_true", help="Send HTML report via webhook email")
        parser.add_argument("--sources", nargs="*", choices=["espn", "fanduel", "wwo"],
                                default=["espn"], help="Projection sources to analyze")
        parser.add_argument("--strategic", action="store_true", help="Include strategic decision analysis")
        parser.add_argument("--ai", choices=["claude", "gemini"], help="Use AI analysis")

    def handle(self, args, fm):
        """Execute the command."""
        week_to_analyze = args.week or self._find_completed_week(fm)
        if not week_to_analyze:
            print("❌ No completed weeks found to analyze")
            return

        print(f"\n🏈 Post-Week Analysis for Week {week_to_analyze}")
        print("=" * 50)

        # Gather analysis data
        analyzer = PostWeekAnalyzer(fm.league)
        
        # Check if week is complete
        if not analyzer.is_week_complete(week_to_analyze):
            print(f"⚠️  Warning: Week {week_to_analyze} may not be complete yet")
            print("   Analysis may be inaccurate for ongoing games")
        
        try:
            # Get projection analysis summary
            projection_summary = self._get_projection_summary(analyzer, week_to_analyze, args.sources)
            
            # Get strategic decision analysis if requested
            strategic_data = None
            if args.strategic:
                strategic_data = self._get_strategic_analysis(week_to_analyze)
            
            # Generate template-based analysis
            context = {
                'week': week_to_analyze,
                'year': fm.league.year,
                'league_name': fm.league.settings.name,
                'projection_summary': projection_summary,
                'my_team_performance': projection_summary.my_team_performance if projection_summary else None,
                'strategic_decisions': strategic_data['outcomes'] if strategic_data else None,
                'strategic_analysis_file': strategic_data['analysis_file'] if strategic_data else None,
            }
            
            # Setup Jinja2 environment and render template
            env = Environment(loader=FileSystemLoader('templates/prompts'))
            template = env.get_template('post_week_analysis.j2')
            analysis_prompt = template.render(context)
            
            if args.ai:
                # Use AI for analysis
                self._generate_ai_analysis(analysis_prompt, args.ai, week_to_analyze)
            else:
                # Save manual prompt
                self._save_manual_prompt(analysis_prompt, week_to_analyze)
            
            # Generate additional outputs if requested
            if args.html and projection_summary:
                html_filename = self._generate_html_report(projection_summary, week_to_analyze)
                
                # Send via email if requested
                if args.email:
                    self._send_email_report(html_filename, week_to_analyze, fm.year)
            
            if args.save and projection_summary:
                self._save_analysis_data(projection_summary, args.save)
                
        except Exception as e:
            print(f"❌ Analysis failed: {e}")
            raise

    def _find_completed_week(self, fm):
        """Find the last completed week."""
        analyzer = PostWeekAnalyzer(fm.league)
        for w in range(fm.league.current_week - 1, 0, -1):
            if analyzer.is_week_complete(w):
                return w
        return None

    def _get_projection_summary(self, analyzer, week, sources):
        """Get projection analysis summary."""
        print("🔍 Analyzing projection accuracy...")
        
        storage = ProjectionStorage()
        stored_projections = storage.load_weekly_projections(week, analyzer.league.year)
        
        external_projections = {}
        if stored_projections:
            print(f"✅ Found stored projections for Week {week}")
            external_projections = storage.convert_to_analysis_format(stored_projections)
            
            # Filter based on requested sources
            filtered_projections = {}
            for source, df in external_projections.items():
                if source.lower() in [s.lower() for s in sources]:
                    filtered_projections[source] = df
            external_projections = filtered_projections
        else:
            print("⚠️  No stored projections found - using current data")

        return analyzer.analyze_week(
            week=week,
            external_projections=external_projections if external_projections else None
        )

    def _get_strategic_analysis(self, week):
        """Get strategic decision analysis."""
        print(f"🎯 Analyzing strategic decisions for Week {week}...")
        
        analyzer = StrategicDecisionAnalyzer()
        analysis_files = analyzer.find_pregame_analysis_files(week)
        
        if not analysis_files:
            print("⚠️  No pre-game analysis files found for strategic validation")
            return None
        
        analysis_file = analysis_files[0]
        decisions = analyzer.parse_pregame_analysis_file(analysis_file, week)
        
        if not decisions:
            print(f"⚠️  No strategic decisions found in {analysis_file}")
            return None
        
        # Mock actual scores for now - in real implementation, get from ESPN API
        # This would be replaced with actual player performance data
        actual_scores = self._get_actual_player_scores(week)
        
        if not actual_scores:
            print("⚠️  Could not load actual player performance data")
            return None
        
        outcomes = analyzer.evaluate_decisions(decisions, actual_scores)
        
        return {
            'outcomes': outcomes,
            'analysis_file': analysis_file
        }

    def _get_actual_player_scores(self, week):
        """Get actual player scores - placeholder for ESPN API integration."""
        # This is a placeholder - in real implementation, this would:
        # 1. Get my team from fantasy manager
        # 2. Loop through all roster players
        # 3. Get their actual points for the specified week
        # 4. Return dictionary of {player_name: points}
        
        # For now, return empty dict to indicate data not available
        return {}

    def _generate_ai_analysis(self, prompt, ai_model, week):
        """Generate AI analysis using the prompt."""
        try:
            from src.ai_integrator import AIIntegrator
            ai_integrator = AIIntegrator()
            
            print(f"🤖 Sending to {ai_model.upper()} for post-week analysis...")
            ai_response = ai_integrator.analyze_post_week(prompt, ai_model)
            
            if ai_response:
                print(f"\n🎯 {ai_model.upper()} POST-WEEK INSIGHTS:")
                print("=" * 60)
                print(ai_response)
                
                # Save analysis
                filename = f"post_week_analysis_week_{week}_{ai_model}.md"
                with open(filename, 'w') as f:
                    f.write(f"# Post-Week Analysis - Week {week}\n\n")
                    f.write(f"**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"**Model:** {ai_model}\n\n")
                    f.write(ai_response)
                print(f"\n📄 Analysis saved: {filename}")
                
                # Send webhook notification
                self._send_webhook_notification(ai_response, week, ai_model)
            else:
                print("❌ AI analysis failed")
                
        except Exception as e:
            print(f"❌ AI error: {e}")
            print("💡 Generating manual prompt instead...")
            self._save_manual_prompt(prompt, week)

    def _save_manual_prompt(self, prompt, week):
        """Save manual prompt for external AI analysis."""
        filename = f"post_week_prompt_week_{week}.md"
        with open(filename, 'w') as f:
            f.write(prompt)
        print(f"📋 Manual prompt saved: {filename}")
        print("💡 Copy to your preferred AI for post-week analysis")

    def _generate_html_report(self, summary, week):
        """Generate HTML report and return filename."""
        html_filename = f"postweek_analysis_week_{week}.html"
        html_generator = HtmlGenerator()
        html_content = html_generator.generate_postweek_report(summary)
        
        with open(html_filename, 'w') as f:
            f.write(html_content)
        print(f"✅ HTML report generated: {html_filename}")
        return html_filename

    def _send_email_report(self, html_filename, week, year):
        """Send HTML report via webhook email."""
        try:
            # Import webhook sender
            from automation.webhook_sender import send_report_webhook
            
            # Read the generated HTML file
            with open(html_filename, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            # Send via webhook
            success = send_report_webhook(
                week=week,
                year=year,
                report_type='post-week-analysis',
                html_content=html_content
            )
            
            if success:
                print(f"📧 Email sent successfully for Week {week}")
                
                # Clean up HTML file after successful email
                try:
                    os.unlink(html_filename)
                    print(f"🗑️  Cleaned up local file: {html_filename}")
                except Exception:
                    pass
            else:
                print(f"❌ Email failed to send for Week {week}")
                print(f"💾 HTML report saved locally: {html_filename}")
                
        except ImportError:
            print("❌ Webhook sender not available - cannot send email")
            print(f"💾 HTML report saved locally: {html_filename}")
        except Exception as e:
            print(f"❌ Email sending failed: {e}")
            print(f"💾 HTML report saved locally: {html_filename}")

    def _save_analysis_data(self, summary, save_path):
        """Save analysis data to file."""
        if save_path.endswith('.json'):
            # Save as JSON
            summary_dict = {
                'week': summary.week,
                'total_players': summary.total_players_analyzed,
                'most_accurate_source': summary.most_accurate_source,
                'least_accurate_source': summary.least_accurate_source,
                'overall_mae': summary.overall_mae,
                'overall_rmse': summary.overall_rmse,
            }
            
            with open(save_path, 'w') as f:
                json.dump(summary_dict, f, indent=2)
            print(f"✅ Analysis saved to {save_path}")
            
        elif save_path.endswith('.csv'):
            print(f"✅ CSV export saved to {save_path}")

    def _send_webhook_notification(self, analysis_content, week, ai_model):
        """Send webhook notification with analysis results."""
        try:
            from automation.webhook_sender import send_analysis_webhook
            
            webhook_data = {
                'week': week,
                'ai_model': ai_model,
                'analysis_type': 'post_week',
                'content_preview': analysis_content[:500] + "..." if len(analysis_content) > 500 else analysis_content
            }
            
            webhook_success = send_analysis_webhook(
                week=week,
                year=2025,  # Would get from fm.league.year
                summary=webhook_data,
                team=None
            )
            
            if webhook_success:
                print("📧 Analysis email sent via webhook")
            else:
                print("⚠️  Webhook notification failed")
                
        except Exception as e:
            print(f"⚠️  Webhook error: {e}")