#!/bin/bash
"""
Setup Post-Week Analysis Automation

Creates a cron job to run post-week analysis every Tuesday at 6:00 AM
and send comprehensive reports via email webhook.

This script specifically targets the post-week analysis with start/sit
intelligence, projection accuracy, and team performance insights.
"""

set -e

PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/." && pwd)"
AUTOMATION_DIR="$PROJECT_DIR/automation"

echo "📊 Setting up Post-Week Analysis Automation"
echo "Project Directory: $PROJECT_DIR"

# Verify dependencies
if ! command -v uv &> /dev/null; then
    echo "❌ Error: 'uv' command not found. Please install uv first."
    exit 1
fi

# Check if automation script exists
if [ ! -f "$AUTOMATION_DIR/generate_and_send_report.py" ]; then
    echo "❌ Error: generate_and_send_report.py not found in $AUTOMATION_DIR"
    exit 1
fi

# Make automation script executable
chmod +x "$AUTOMATION_DIR/generate_and_send_report.py"

echo "🔍 Checking webhook configuration..."

# Check webhook configuration in .env file
if [ -f "$PROJECT_DIR/.env" ] && grep -q "FANTASY_WEBHOOK_URL" "$PROJECT_DIR/.env"; then
    echo "✅ Webhook configuration found in .env - emails will be sent"
else
    echo "⚠️  WARNING: No webhook configuration found!"
    echo "   Post-week reports will be generated but not emailed"
    echo "   To enable email delivery:"
    echo "   1. Add FANTASY_WEBHOOK_URL to your .env file"
    echo "   2. Add FANTASY_WEBHOOK_SECRET to your .env file"
    echo "   3. See .env.example for format"
    echo ""
    read -p "Continue without email setup? [y/N]: " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ Setup cancelled. Configure webhook in .env first."
        exit 1
    fi
fi

# Create the cron entry for Tuesday 6 AM
CRON_ENTRY="
# Fantasy AI Post-Week Analysis - Tuesday 6:00 AM ET  
0 6 * * 2 cd $PROJECT_DIR && /usr/local/bin/uv run python cli.py postweek --html --email --sources espn fanduel wwo --strategic >> $AUTOMATION_DIR/postweek_automation.log 2>&1
"

# Backup existing crontab
echo "📋 Backing up existing crontab..."
crontab -l > "$AUTOMATION_DIR/crontab_backup_postweek_$(date +%Y%m%d_%H%M%S).txt" 2>/dev/null || echo "No existing crontab found"

# Check if entry already exists
if crontab -l 2>/dev/null | grep -q "post-week-analysis"; then
    echo "⚠️  Post-week automation cron job already exists!"
    echo "Current post-week entries:"
    crontab -l 2>/dev/null | grep "post-week"
    echo ""
    read -p "Replace existing entry? [y/N]: " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        # Remove existing postweek entries and add new one
        (crontab -l 2>/dev/null | grep -v "post-week-analysis" || true; echo "$CRON_ENTRY") | crontab -
        echo "✅ Existing entry replaced"
    else
        echo "❌ Setup cancelled to avoid duplicates"
        exit 1
    fi
else
    # Add new cron entry
    echo "⚙️  Installing post-week analysis cron job..."
    (crontab -l 2>/dev/null || true; echo "$CRON_ENTRY") | crontab -
    echo "✅ Post-week analysis cron job installed!"
fi

# Create log file
touch "$AUTOMATION_DIR/postweek_automation.log"

echo ""
echo "🎉 Post-Week Analysis Automation Setup Complete!"
echo "=" * 60
echo "📅 Schedule: Every Tuesday at 6:00 AM ET"
echo "📊 Analysis: Projection accuracy + Start/sit intelligence"
echo "📧 Email: $([ -f "$PROJECT_DIR/.env" ] && grep -q "FANTASY_WEBHOOK_URL" "$PROJECT_DIR/.env" && echo "Enabled" || echo "Disabled (no webhook config in .env)")"
echo "📁 Logs: $AUTOMATION_DIR/postweek_automation.log"
echo ""
echo "🔧 Commands:"
echo "   View cron jobs:    crontab -l"
echo "   Test automation:   cd $PROJECT_DIR && uv run python cli.py postweek --html --email --week 1 --sources espn fanduel wwo --strategic"
echo "   Manual run:        cd $PROJECT_DIR && uv run python cli.py postweek --html --email --sources espn fanduel wwo --strategic"
echo ""

# Show next scheduled runs
echo "📅 Next scheduled runs:"
echo "   $(date -v+1d -v6H -v0M -v0S 2>/dev/null || date -d 'next tuesday 06:00' 2>/dev/null || echo 'Next Tuesday at 6:00 AM')"

# Test webhook if configured
if [ -f "$PROJECT_DIR/.env" ] && grep -q "FANTASY_WEBHOOK_URL" "$PROJECT_DIR/.env"; then
    echo ""
    read -p "Test webhook connection? [y/N]: " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "🧪 Testing webhook connection..."
        cd "$PROJECT_DIR"
        if uv run python -c "
from automation.webhook_sender import send_report_webhook
success = send_report_webhook(1, 2025, 'test', '<h1>Test Report</h1><p>Webhook test successful!</p>')
print('✅ Webhook test successful!' if success else '❌ Webhook test failed!')
        "; then
            echo "✅ Webhook is working correctly"
        else
            echo "❌ Webhook test failed - check .env configuration"
        fi
    fi
fi

echo ""
echo "⚠️  Important:"
echo "   • Keep your computer on Tuesday mornings at 6 AM"
echo "   • Check logs if you don't receive emails"
echo "   • Analysis runs automatically after Monday Night Football"
echo ""
echo "🚀 Automation is now active! You'll receive Tuesday morning post-game analysis reports."